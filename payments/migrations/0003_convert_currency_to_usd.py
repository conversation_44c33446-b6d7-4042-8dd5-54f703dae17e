# Generated by Django

from django.db import migrations


def convert_currency_to_usd(apps, schema_editor):
    """
    Convert all existing currency fields from INR to USD
    Note: This is a data migration - you may want to convert amounts as well
    based on exchange rates if needed.
    """
    PaymentSession = apps.get_model('payments', 'PaymentSession')
    PaymentHistory = apps.get_model('payments', 'PaymentHistory')
    
    # Update PaymentSession records
    PaymentSession.objects.filter(currency='INR').update(currency='USD')
    
    # Update PaymentHistory records  
    PaymentHistory.objects.filter(currency='INR').update(currency='USD')


def revert_currency_to_inr(apps, schema_editor):
    """
    Revert currency fields back to INR
    """
    PaymentSession = apps.get_model('payments', 'PaymentSession')
    PaymentHistory = apps.get_model('payments', 'PaymentHistory')
    
    # Revert PaymentSession records
    PaymentSession.objects.filter(currency='USD').update(currency='INR')
    
    # Revert PaymentHistory records
    PaymentHistory.objects.filter(currency='USD').update(currency='INR')


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0002_remove_package_stripe_price_id'),
    ]

    operations = [
        migrations.RunPython(
            convert_currency_to_usd,
            revert_currency_to_inr,
        ),
    ]