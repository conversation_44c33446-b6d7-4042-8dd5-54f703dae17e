"""
Enhanced video creation service with provider architecture
SIMPLIFIED: Direct provider execution without intermediate Kafka events
Only uses stage events (StageInitiated, StageCompleted, StageFailed)
"""
import logging
import uuid
from typing import Dict, Any, Optional

from ..models import Video
from ..constants import (
    VIDEO_CREATION_FLOW, STAGE_PROVIDERS
)
from .providers import ProviderFactory, get_provider_for_stage, ProcessingStatus
from events.producer import event_publisher

logger = logging.getLogger(__name__)


class VideoCreationService:
    """
    Enhanced video creation service with provider architecture
    Handles both event-driven external providers and direct internal processing
    """
    
    def __init__(self):
        # Initialize provider factory
        ProviderFactory.initialize()
    
    @staticmethod
    def generate_correlation_id(workflow_type: str, video_id: int, additional_context: str = '') -> str:
        """
        Generate pattern-based correlation IDs for different workflow types
        
        Args:
            workflow_type: Type of workflow (e.g., 'video-creation', 'manual-progression', 'retry', 'callback')
            video_id: Video ID for tracking
            additional_context: Additional context if needed
            
        Returns:
            Formatted correlation ID string
        """
        import time
        timestamp = int(time.time())
        
        if additional_context:
            return f'{workflow_type}-{timestamp}-{video_id}-{additional_context}'
        else:
            return f'{workflow_type}-{timestamp}-{video_id}'
    
    def handle_stage_completion(self, video_id: int, stage: str, correlation_id: str, stage_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Handle stage completion and determine next stage
        
        Args:
            video_id: Video ID
            stage: Completed stage name
            correlation_id: Correlation ID for tracking
            stage_data: Optional stage completion data
            
        Returns:
            Dict with success status and next action
        """
        try:
            # Get video instance
            video = Video.objects.get(id=video_id)
            
            logger.info(f"Handling stage completion: {stage} for video {video_id}")
            
            # Update workflow state
            from events.models import WorkflowState
            workflow_state, created = WorkflowState.objects.get_or_create(
                video_id=video,
                defaults={
                    'correlation_id': correlation_id,
                    'current_stage': stage
                }
            )
            
            # Mark stage as completed
            workflow_state.mark_stage_completed(stage)
            
            # Determine next stage
            from ..constants import VIDEO_CREATION_FLOW
            try:
                current_index = VIDEO_CREATION_FLOW.index(stage)
                next_stage = VIDEO_CREATION_FLOW[current_index + 1] if current_index < len(VIDEO_CREATION_FLOW) - 1 else None
            except ValueError:
                logger.error(f"Unknown stage: {stage}")
                return {'success': False, 'error': f'Unknown stage: {stage}'}
            
            if next_stage:
                # Check if auto approval is enabled
                if video.auto_approval_each_stage:
                    # Auto approval enabled - proceed automatically to next stage
                    logger.info(f"Auto approval enabled - proceeding automatically to next stage: {next_stage}")
                    
                    # Update workflow state
                    workflow_state.current_stage = next_stage
                    workflow_state.save(update_fields=['current_stage', 'updated_at'])
                    
                    # Update video stage
                    video.stage = next_stage
                    video.save(update_fields=['stage'])
                    
                    # Initiate next stage
                    success = self.handle_stage_initiation(video, next_stage, correlation_id)
                    
                    if success:
                        return {
                            'success': True, 
                            'action': 'next_stage_initiated',
                            'next_stage': next_stage,
                            'auto_approval': True
                        }
                    else:
                        return {
                            'success': False, 
                            'error': f'Failed to initiate next stage {next_stage}',
                            'next_stage': next_stage
                        }
                else:
                    # Auto approval disabled - wait for manual approval
                    logger.info(f"Auto approval disabled - waiting for manual approval before proceeding to {next_stage}")
                    
                    # Update video status to indicate stage completion awaiting approval
                    video.status = 'waiting_for_review'
                    video.save(update_fields=['status'])
                    
                    return {
                        'success': True,
                        'action': 'waiting_for_review',
                        'next_stage': next_stage,
                        'auto_approval': False
                    }
            else:
                # All stages completed
                workflow_state.current_stage = 'completed'
                workflow_state.save(update_fields=['current_stage', 'updated_at'])
                
                video.stage = 'completed'
                video.status = 'done'
                video.save(update_fields=['stage', 'status'])
                
                logger.info(f"Video {video_id} creation completed successfully")
                
                # Publish video completion event
                try:
                    from events.producer import event_publisher
                    event_publisher.publish_video_completed(
                        video_id=video_id,
                        correlation_id=correlation_id,
                        completion_data=stage_data or {}
                    )
                except Exception as e:
                    logger.error(f"Failed to publish video completion event: {e}")
                
                return {
                    'success': True,
                    'action': 'video_completed',
                    'final_stage': stage
                }
                
        except Video.DoesNotExist:
            error_msg = f"Video {video_id} not found"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}
            
        except Exception as e:
            error_msg = f"Error handling stage completion: {str(e)}"
            logger.error(f"Error handling stage completion for video {video_id}, stage {stage}: {error_msg}")
            return {'success': False, 'error': error_msg}
    
    def handle_stage_failure(self, video_id: int, stage: str, correlation_id: str, error_message: str, error_code: str = None) -> Dict[str, Any]:
        """
        Handle stage failure and determine retry strategy
        
        Args:
            video_id: Video ID
            stage: Failed stage name
            correlation_id: Correlation ID for tracking
            error_message: Error message
            error_code: Optional error code
            
        Returns:
            Dict with success status and retry action
        """
        try:
            # Get video instance
            video = Video.objects.get(id=video_id)
            
            logger.warning(f"Handling stage failure: {stage} for video {video_id}, error: {error_message}")
            
            # Update workflow state
            from events.models import WorkflowState, RetryPolicy
            workflow_state, created = WorkflowState.objects.get_or_create(
                video_id=video,
                defaults={
                    'correlation_id': correlation_id,
                    'current_stage': stage
                }
            )
            
            # Get current retry count BEFORE marking as failed
            current_retry_count = workflow_state.get_retry_count(stage)
            
            # Get retry policy
            retry_policy = RetryPolicy.objects.filter(
                event_type='StageFailure',
                stage=stage,
                is_active=True
            ).first()
            
            if not retry_policy:
                retry_policy = RetryPolicy.objects.filter(
                    event_type='StageFailure',
                    stage__isnull=True,
                    is_active=True
                ).first()
            
            max_retries = retry_policy.max_retries if retry_policy else 3
            
            # Check if we should retry
            if current_retry_count < max_retries:
                # Mark stage as failed (this increments retry count)
                workflow_state.mark_stage_failed(stage)
                new_retry_count = workflow_state.get_retry_count(stage)
                
                # Calculate delay with exponential backoff
                delay_seconds = retry_policy.calculate_delay(new_retry_count - 1) if retry_policy else (60 * (2 ** (new_retry_count - 1)))
                delay_seconds = min(delay_seconds, 3600)  # Max 1 hour delay
                
                logger.warning(f"Scheduling retry {new_retry_count}/{max_retries} for stage {stage} of video {video_id} "
                           f"after {delay_seconds} seconds")
                
                # Schedule retry
                import time
                from threading import Timer
                
                def delayed_retry():
                    try:
                        # FIXED: Use _automatic_retry_stage instead of retry_stage to preserve retry counts
                        success = self._automatic_retry_stage(video_id, stage, new_retry_count, max_retries)
                        if success:
                            logger.info(f"Successfully initiated automatic retry {new_retry_count}/{max_retries} for stage {stage}, video {video_id}")
                        else:
                            logger.error(f"Failed to initiate automatic retry for stage {stage}, video {video_id}")
                    except Exception as e:
                        logger.error(f"Error in delayed automatic retry for stage {stage}: {e}")
                
                timer = Timer(delay_seconds, delayed_retry)
                timer.daemon = True
                timer.start()
                
                # Update video status
                video.status = 'in_progress'
                video.error = f"Retry {new_retry_count}/{max_retries} scheduled: {error_message}"
                video.save(update_fields=['status', 'error'])
                
                return {
                    'success': True,
                    'action': 'retry_scheduled',
                    'retry_count': new_retry_count,
                    'max_retries': max_retries,
                    'delay_seconds': delay_seconds
                }
            else:
                # Max retries exceeded
                logger.error(f"Max retries ({max_retries}) exceeded for stage {stage}, video {video_id}")
                
                video.status = 'error'
                video.error = f"Stage {stage} failed after {max_retries} retries: {error_message}"
                video.save(update_fields=['status', 'error'])
                
                return {
                    'success': True,
                    'action': 'max_retries_exceeded',
                    'retry_count': current_retry_count,
                    'max_retries': max_retries
                }
                
        except Video.DoesNotExist:
            error_msg = f"Video {video_id} not found"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}
            
        except Exception as e:
            error_msg = f"Error handling stage failure: {str(e)}"
            logger.error(f"Error handling stage failure for video {video_id}, stage {stage}: {error_msg}")
            return {'success': False, 'error': error_msg}

    def start_video_creation(self, video: Video) -> bool:
        """
        Start the video creation process by publishing initial events
        
        Args:
            video: Video instance
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Generate correlation ID for tracking - Use pattern based on creation type
            correlation_id = self.generate_correlation_id('video-creation', video.id)
            
            # Set initial status and save correlation_id to Video model
            video.status = 'in_progress'
            video.stage = 'script_generation'
            video.correlation_id = correlation_id
            video.save()
            
            # Create workflow state
            from events.models import WorkflowState
            WorkflowState.objects.create(
                video_id=video,
                correlation_id=correlation_id,
                current_stage='script_generation'
            )
            
            # Publish stage initiation event for first stage
            success = event_publisher.publish_stage_initiated(
                video_id=video.id,
                stage='script_generation',
                correlation_id=correlation_id
            )
            
            if not success:
                logger.error(f"Failed to publish StageInitiated event for video {video.id}")
                return False
            
            logger.info(f"Started enhanced video creation for video {video.id}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting video creation for video {video.id}: {str(e)}")
            video.status = 'error'
            video.error = str(e)
            video.save()
            return False
    
    def handle_stage_initiation(self, video: Video, stage: str, correlation_id: str) -> bool:
        """
        Handle stage initiation using appropriate provider or internal processing
        
        Args:
            video: Video instance
            stage: The stage to initiate
            correlation_id: Correlation ID for tracking
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Handling stage initiation: {stage} for video {video.id}")
            
            # Validate stage
            if stage not in VIDEO_CREATION_FLOW:
                logger.error(f"Invalid stage: {stage}")
                return False
            
            # Route to appropriate handler based on provider
            provider_name = STAGE_PROVIDERS.get(stage)
            
            if provider_name == 'internal':
                return self._handle_internal_stage(video, stage, correlation_id)
            else:
                return self._handle_external_stage(video, stage, correlation_id)
                
        except Exception as e:
            logger.error(f"Error handling stage initiation for video {video.id}, stage {stage}: {str(e)}")
            return False
    
    def retry_stage(self, video: Video, stage: str, requested_by: str, reason: Optional[str] = None) -> Dict[str, Any]:
        """
        Retry a specific stage of video creation process with full validation and Kafka event publishing
        
        Args:
            video: Video instance
            stage: The stage to retry
            requested_by: User ID or identifier who requested the retry
            reason: Optional reason for retry
            
        Returns:
            Dict containing success status, messages, and relevant data
        """
        try:
            logger.info(f"Starting retry for video {video.id}, stage: {stage}, requested by: {requested_by}")
            
            # Validate stage
            if stage not in VIDEO_CREATION_FLOW:
                return {
                    'success': False,
                    'error': f'Invalid stage: {stage}',
                    'error_code': 'INVALID_STAGE',
                    'valid_stages': VIDEO_CREATION_FLOW
                }
            
            # Check if video is in a state that allows retry
            if video.status not in ['error', 'done', 'in_progress']:
                return {
                    'success': False,
                    'error': f'Cannot retry stage. Video status is "{video.status}". Retry is only allowed for videos with status "error", "done", or "in_progress".',
                    'error_code': 'INVALID_VIDEO_STATUS',
                    'current_status': video.status
                }
            
            # Check stage dependencies - ensure previous stages are completed
            stage_index = VIDEO_CREATION_FLOW.index(stage)
            previous_stages = VIDEO_CREATION_FLOW[:stage_index]
            
            # Get or create workflow state
            from events.models import WorkflowState
            try:
                workflow_state = WorkflowState.objects.get(video_id=video)
            except WorkflowState.DoesNotExist:
                # Create workflow state if it doesn't exist
                initial_correlation_id = self.generate_correlation_id('workflow-recovery', video.id, stage)
                workflow_state = WorkflowState.objects.create(
                    video_id=video,
                    correlation_id=initial_correlation_id,
                    current_stage=stage
                )
                logger.info(f"Created new workflow state for video {video.id}")
            
            # Validate that all previous stages are completed
            incomplete_stages = []
            for prev_stage in previous_stages:
                if not workflow_state.is_stage_completed(prev_stage):
                    incomplete_stages.append(prev_stage)
            
            if incomplete_stages:
                return {
                    'success': False,
                    'error': f'Cannot retry stage "{stage}". Previous stages must be completed first.',
                    'error_code': 'INCOMPLETE_DEPENDENCIES',
                    'incomplete_stages': incomplete_stages,
                    'completed_stages': workflow_state.stages_completed,
                    'suggestion': f'Please complete or retry the incomplete stages first: {", ".join(incomplete_stages)}'
                }
            
            # Reset retry count for the stage (fresh start on manual retry)
            logger.info(f"Resetting retry count for stage {stage} of video {video.id}")
            if stage in workflow_state.retry_counts:
                old_retry_count = workflow_state.retry_counts[stage]
                workflow_state.retry_counts[stage] = 0  # Reset to 0 so next failure starts at 1
                workflow_state.save(update_fields=['retry_counts', 'updated_at'])
                logger.info(f"Reset retry count from {old_retry_count} to 0 for stage {stage}")
            else:
                logger.info(f"No previous retry count found for stage {stage}")
            
            # Generate new correlation ID for retry using enhanced pattern
            retry_correlation_id = self.generate_correlation_id('retry', video.id, stage)
            
            # Update video state for retry and save correlation_id
            video.status = 'in_progress'
            video.stage = stage
            video.error = None  # Clear previous error
            video.correlation_id = retry_correlation_id
            video.save()
            
            # Update workflow state
            workflow_state.current_stage = stage
            workflow_state.correlation_id = retry_correlation_id
            workflow_state.is_paused = False
            # Remove stage from failed list if it was there
            if stage in workflow_state.stages_failed:
                workflow_state.stages_failed.remove(stage)
            workflow_state.save()

            stage_success = event_publisher.publish_stage_initiated(
                video_id=video.id,
                stage=stage,
                correlation_id=retry_correlation_id
            )
            
            if stage_success:
                logger.info(f"Successfully initiated retry for video {video.id}, stage {stage}")
                
                return {
                    'success': True,
                    'message': f'Stage "{stage}" retry initiated successfully',
                    'data': {
                        'video_id': video.id,
                        'stage': stage,
                        'correlation_id': retry_correlation_id,
                        'video_status': video.status,
                        'workflow_state': {
                            'current_stage': workflow_state.current_stage,
                            'completed_stages': workflow_state.stages_completed,
                            'failed_stages': workflow_state.stages_failed,
                            'retry_counts': workflow_state.retry_counts
                        }
                    }
                }
            else:
                # Retry initiation failed
                video.status = 'error'
                video.error = f'Failed to initiate retry for stage "{stage}"'
                video.save()
                
                logger.error(f"Failed to initiate retry for video {video.id}, stage {stage}")
                
                return {
                    'success': False,
                    'error': f'Failed to initiate retry for stage "{stage}"',
                    'error_code': 'RETRY_INITIATION_FAILED',
                    'data': {
                        'video_id': video.id,
                        'stage': stage,
                    }
                }
                
        except Exception as e:
            logger.error(f"Error during retry initiation for video {video.id}, stage {stage}: {str(e)}")
            
            # Update video with error
            video.status = 'error'
            video.error = f'Retry initiation error: {str(e)}'
            video.save()
            
            # Publish stage failed event
            try:
                event_publisher.publish_stage_failed(
                    video_id=video.id,
                    stage=stage,
                    correlation_id=getattr(video, 'correlation_id', str(uuid.uuid4())),
                    error_message=f'Retry initiation exception: {str(e)}',
                    error_code='RETRY_EXCEPTION'
                )
            except Exception as event_error:
                logger.error(f"Failed to publish stage failed event: {event_error}")
            
            return {
                'success': False,
                'error': f'Retry initiation failed: {str(e)}',
                'error_code': 'RETRY_EXCEPTION',
                'data': {
                    'video_id': video.id,
                    'stage': stage
                }
            }
    
    def _automatic_retry_stage(self, video_id: int, stage: str, retry_attempt: int, max_retries: int) -> bool:
        """
        Perform automatic retry without resetting retry counts (used by automatic retry mechanism)
        
        Args:
            video_id: Video ID
            stage: The stage to retry
            retry_attempt: Current retry attempt number
            max_retries: Maximum retries allowed
            
        Returns:
            bool: True if retry was initiated successfully, False otherwise
        """
        try:
            video = Video.objects.get(id=video_id)
            
            logger.info(f"Executing automatic retry {retry_attempt}/{max_retries} for stage {stage}, video {video_id}")
            
            # Generate new correlation ID for this retry attempt
            retry_correlation_id = self.generate_correlation_id('auto-retry', video_id, f'{stage}-{retry_attempt}')
            
            # Update video state for retry (but don't reset retry counts)
            video.status = 'in_progress'
            video.stage = stage
            video.correlation_id = retry_correlation_id
            video.error = f"Automatic retry {retry_attempt}/{max_retries} in progress"
            video.save()
            
            # Update workflow state current stage (but preserve retry counts)
            from events.models import WorkflowState
            try:
                workflow_state = WorkflowState.objects.get(video_id=video)
                workflow_state.current_stage = stage
                workflow_state.correlation_id = retry_correlation_id
                workflow_state.is_paused = False
                workflow_state.save(update_fields=['current_stage', 'correlation_id', 'is_paused', 'updated_at'])
                
                logger.info(f"Preserved retry count for stage {stage}: {workflow_state.get_retry_count(stage)}")
                
            except WorkflowState.DoesNotExist:
                logger.warning(f"WorkflowState not found for video {video_id} during automatic retry")
            
            # Directly publish stage initiated event without resetting retry counts
            success = event_publisher.publish_stage_initiated(
                video_id=video_id,
                stage=stage,
                correlation_id=retry_correlation_id
            )
            
            if success:
                logger.info(f"Successfully initiated automatic retry {retry_attempt}/{max_retries} for stage {stage}, video {video_id}")
                return True
            else:
                logger.error(f"Failed to initiate automatic retry for stage {stage}, video {video_id}")
                return False
                
        except Video.DoesNotExist:
            logger.error(f"Video {video_id} not found during automatic retry")
            return False
        except Exception as e:
            logger.error(f"Error in automatic retry for stage {stage}, video {video_id}: {e}")
            return False
    
    def _handle_external_stage(self, video: Video, stage: str, correlation_id: str) -> bool:
        """
        Handle external stage processing using providers DIRECTLY - no Kafka events
        
        Args:
            video: Video instance
            stage: Stage name
            correlation_id: Correlation ID for tracking
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get appropriate provider for this stage
            provider = get_provider_for_stage(stage, video)
            
            # Log provider selection
            logger.info(f"Executing provider '{provider.provider_name}' directly for stage '{stage}', video {video.id}")
            
            # DIRECT EXECUTION: Execute provider stage directly without Kafka events
            response = provider.process_stage(video, stage, correlation_id)
            
            if response.status == ProcessingStatus.SUCCESS:
                logger.info(f"Provider {provider.provider_name} successfully executed stage {stage} for video {video.id}")
                
                # For synchronous providers, publish stage completed directly
                if provider.provider_name in ['nca', 'together_ai', 'runpod']:
                    success = event_publisher.publish_stage_completed(
                        video_id=video.id,
                        stage=stage,
                        correlation_id=correlation_id,
                        stage_data={'provider_response': response.data}
                    )
                    
                    if not success:
                        logger.error(f"Failed to publish stage completed event after provider success")
                        return False
                
                # For N8N (async), we still wait for webhook callbacks
                # but no intermediate Kafka events
                return True
                
            elif response.status == ProcessingStatus.PENDING:
                # For async providers like N8N, just wait for callback
                logger.info(f"Provider {provider.provider_name} stage {stage} is pending for video {video.id} - waiting for callback")
                return True
                
            else:
                # Handle failure - publish stage failed directly
                error_msg = response.error_message or "Provider execution failed"
                logger.error(f"Provider {provider.provider_name} failed stage {stage} for video {video.id}: {error_msg}")
                
                # Publish stage failed event directly
                return event_publisher.publish_stage_failed(
                    video_id=video.id,
                    stage=stage,
                    correlation_id=correlation_id,
                    error_message=error_msg,
                    error_code='PROVIDER_EXECUTION_FAILED'
                )
                
        except Exception as e:
            logger.error(f"Error in direct external stage processing for {stage}: {str(e)}")
            
            # Publish stage failed event directly
            return event_publisher.publish_stage_failed(
                video_id=video.id,
                stage=stage,
                correlation_id=correlation_id,
                error_message=str(e),
                error_code='EXTERNAL_STAGE_EXCEPTION'
            )
    
    def _handle_internal_stage(self, video: Video, stage: str, correlation_id: str) -> bool:
        """
        Handle internal stage processing DIRECTLY - no Kafka events
        
        Args:
            video: Video instance
            stage: Stage name
            correlation_id: Correlation ID for tracking
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Executing internal stage directly: '{stage}', video {video.id}")
            
            # DIRECT EXECUTION: Execute internal stage directly without Kafka events
            if stage == 'track_creation':
                from .internal_stage_processor import internal_stage_processor
                result = internal_stage_processor.process_track_creation(video.id)
                
                if result['success']:
                    # Publish stage completed event directly
                    return event_publisher.publish_stage_completed(
                        video_id=video.id,
                        stage=stage,
                        correlation_id=correlation_id,
                        stage_data=result
                    )
                else:
                    # Publish stage failed event directly
                    return event_publisher.publish_stage_failed(
                        video_id=video.id,
                        stage=stage,
                        correlation_id=correlation_id,
                        error_message=result.get('error', 'Unknown error'),
                        error_code=result.get('error_code', 'INTERNAL_STAGE_FAILED')
                    )
            
            elif stage == 'clip_creation':
                from .internal_stage_processor import internal_stage_processor
                result = internal_stage_processor.process_clip_creation(video.id)
                
                if result['success']:
                    # Publish stage completed event directly
                    return event_publisher.publish_stage_completed(
                        video_id=video.id,
                        stage=stage,
                        correlation_id=correlation_id,
                        stage_data=result
                    )
                else:
                    # Publish stage failed event directly
                    return event_publisher.publish_stage_failed(
                        video_id=video.id,
                        stage=stage,
                        correlation_id=correlation_id,
                        error_message=result.get('error', 'Unknown error'),
                        error_code=result.get('error_code', 'INTERNAL_STAGE_FAILED')
                    )
            
            else:
                logger.error(f"Unknown internal stage: {stage}")
                return event_publisher.publish_stage_failed(
                    video_id=video.id,
                    stage=stage,
                    correlation_id=correlation_id,
                    error_message=f"Unknown internal stage: {stage}",
                    error_code='UNKNOWN_INTERNAL_STAGE'
                )
                
        except Exception as e:
            logger.error(f"Error in direct internal stage processing for {stage}: {str(e)}")
            
            # Publish stage failed event directly
            return event_publisher.publish_stage_failed(
                video_id=video.id,
                stage=stage,
                correlation_id=correlation_id,
                error_message=str(e),
                error_code='INTERNAL_STAGE_EXCEPTION'
            )
    
    def proceed_to_next_stage(self, video: Video, feedback: str = '', 
                             reason: str = '', requested_by: str = 'system') -> Dict[str, Any]:
        """
        Manually proceed to the next stage of video creation process
        Used when auto_approval_each_stage is False
        
        Args:
            video: Video instance
            feedback: Optional feedback for the current stage
            reason: Reason for approval
            requested_by: User ID who requested the progression
            
        Returns:
            Dict with success status, message, and additional data
        """
        try:
            from ..constants import VIDEO_CREATION_FLOW
            
            # Validate video limits before proceeding (for user's peace of mind)
            if video.user:
                from payments.services import UsageService
                can_create, message_or_details = UsageService.can_create_video(video.user, video.duration or 30)
                if not can_create and isinstance(message_or_details, dict):
                    error_details = message_or_details
                    return {
                        'success': False,
                        'error': error_details.get('error', 'Usage limit validation failed'),
                        'error_code': error_details.get('error_code', 'USAGE_LIMIT_EXCEEDED'),
                        'data': {
                            'video_id': video.id,
                            'usage_type': error_details.get('usage_type', ''),
                            'upgrade_required': error_details.get('upgrade_required', True)
                        }
                    }
            
            # Validate video state
            if video.auto_approval_each_stage:
                return {
                    'success': False,
                    'error': 'Video has auto_approval_each_stage enabled. Manual progression not needed.',
                    'error_code': 'AUTO_APPROVAL_ENABLED',
                    'data': {
                        'video_id': video.id,
                        'auto_approval_enabled': True
                    }
                }
            
            current_stage = video.stage
            
            # Check if video is already completed
            if current_stage == 'completed' and video.status == 'done':
                return {
                    'success': False,
                    'error': 'Video creation is already completed. No further stages to proceed to.',
                    'error_code': 'VIDEO_ALREADY_COMPLETED',
                    'data': {
                        'video_id': video.id,
                        'current_stage': current_stage,
                        'current_status': video.status
                    }
                }

            if video.status not in ['waiting_for_review', 'done']:
                return {
                    'success': False,
                    'error': f'Cannot proceed from current video status: {video.status}. Stage must be completed first.',
                    'error_code': 'INVALID_VIDEO_STATUS',
                    'data': {
                        'video_id': video.id,
                        'current_status': video.status,
                        'required_status': 'waiting_for_review'
                    }
                }
            
            if current_stage not in VIDEO_CREATION_FLOW:
                return {
                    'success': False,
                    'error': f'Invalid current stage: {current_stage}',
                    'error_code': 'INVALID_STAGE',
                    'data': {
                        'video_id': video.id,
                        'current_stage': current_stage
                    }
                }
            
            # Generate correlation ID for tracking - Enhanced pattern for manual progression
            correlation_id = self.generate_correlation_id('manual-progression', video.id)
            
            # Update video's correlation_id for tracking
            video.correlation_id = correlation_id
            
            # Check if this is the last stage
            current_index = VIDEO_CREATION_FLOW.index(current_stage)
            
            if current_index >= len(VIDEO_CREATION_FLOW) - 1:
                # This is the last stage - mark video as completed
                video.status = 'done'
                video.stage = 'completed'
                video.error = None
                video.save()
                
                return {
                    'success': True,
                    'message': 'Video creation completed successfully',
                    'action': 'video_completed',
                    'video_id': video.id,
                    'final_stage': current_stage,
                    'correlation_id': correlation_id
                }
            
            # Move to next stage
            next_stage = VIDEO_CREATION_FLOW[current_index + 1]
            
            # Update video to next stage
            video.stage = next_stage
            video.status = 'in_progress'
            video.error = None  # Clear any previous errors
            video.save()
            
            # Use existing stage initiation flow - no special approval events needed
            success = event_publisher.publish_stage_initiated(
                video_id=video.id,
                stage=next_stage,
                correlation_id=correlation_id
            )
            
            if success:
                logger.info(f"Manual progression: video {video.id} moved from {current_stage} to {next_stage}")
                
                return {
                    'success': True,
                    'message': f'Stage {current_stage} approved and proceeded to {next_stage}',
                    'action': 'stage_proceeded',
                    'video_id': video.id,
                    'previous_stage': current_stage,
                    'current_stage': next_stage,
                    'feedback': feedback,
                    'correlation_id': correlation_id,
                    'next_stage_initiated': True
                }
            else:
                # Revert video state if stage initiation failed
                video.stage = current_stage
                video.status = 'error'
                video.error = f'Failed to initiate next stage: {next_stage}'
                video.save()
                
                return {
                    'success': False,
                    'error': f'Stage {current_stage} approved but failed to initiate {next_stage}',
                    'error_code': 'NEXT_STAGE_INITIATION_FAILED',
                    'data': {
                        'video_id': video.id,
                        'previous_stage': current_stage,
                        'failed_stage': next_stage,
                        'correlation_id': correlation_id
                    }
                }
                
        except Exception as e:
            logger.error(f"Error during manual stage progression for video {video.id}: {str(e)}")
            
            # Update video with error
            video.status = 'error'
            video.error = f'Manual progression error: {str(e)}'
            video.save()
            
            return {
                'success': False,
                'error': f'Manual progression failed: {str(e)}',
                'error_code': 'PROGRESSION_EXCEPTION',
                'data': {
                    'video_id': video.id,
                    'stage': video.stage
                }
            }

# Global service instance
video_creation_service = VideoCreationService()
