"""
Runpod Provider for image generation
Handles image_generation stage using Runpod API
"""
import logging
import requests
import uuid
import time
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, Any, Optional, List
from django.conf import settings

from .base import BaseServiceProvider, ProviderResponse, ProcessingStatus, ProviderError, CallbackMixin

logger = logging.getLogger(__name__)


class RunpodProvider(BaseServiceProvider):
    """
    Provider for Runpod image generation API
    Supports AI-powered image generation for videos using Runpod infrastructure
    """
    
    # Stages supported by Runpod
    SUPPORTED_STAGES = [
        'image_generation'
    ]
    
    def __init__(self):
        super().__init__('runpod')
        self.api_key = getattr(settings, 'RUNPOD_API_KEY', '')
        self.endpoint_id = getattr(settings, 'RUNPOD_ENDPOINT_ID', '')
        self.base_url = getattr(settings, 'RUNPOD_BASE_URL', '')
        self.timeout = getattr(settings, 'RUNPOD_REQUEST_TIMEOUT', 300)  # 5 minutes
        self.max_retries = getattr(settings, 'RUNPOD_MAX_RETRIES', 3)
        self.poll_interval = getattr(settings, 'RUNPOD_POLL_INTERVAL', 5)  # seconds
        self.batch_size = getattr(settings, 'RUNPOD_BATCH_SIZE', 5)  # Max concurrent jobs
        self.max_concurrent_polls = getattr(settings, 'RUNPOD_MAX_CONCURRENT_POLLS', 10)
    
    def get_supported_stages(self) -> list:
        """Get list of stages this provider supports"""
        return self.SUPPORTED_STAGES.copy()
    
    def process_stage(
        self, 
        video, 
        stage: str, 
        correlation_id: str, 
        payload: Optional[Dict[str, Any]] = None
    ) -> ProviderResponse:
        """
        Process image generation using Runpod API
        
        Args:
            video: Video model instance
            stage: Stage name (should be 'image_generation')
            correlation_id: Correlation ID for tracking
            payload: Optional additional payload data
            
        Returns:
            ProviderResponse: Response with generated images
        """
        try:
            self.log_operation('process_stage', video.id, stage, {
                'correlation_id': correlation_id
            })
            
            # Validate stage
            if not self.validate_stage(stage):
                raise ProviderError(
                    f"Stage '{stage}' not supported by Runpod provider",
                    self.provider_name,
                    stage,
                    'UNSUPPORTED_STAGE'
                )
            
            # Check API configuration
            if not self.api_key or not self.endpoint_id:
                raise ProviderError(
                    "Runpod API key or endpoint ID not configured",
                    self.provider_name,
                    stage,
                    'MISSING_API_CONFIG'
                )
            
            # Get media generations that need image generation
            media_generations = self._get_media_generations_for_processing(video)
            if not media_generations:
                raise ProviderError(
                    "No media generations found for image processing",
                    self.provider_name,
                    stage,
                    'MISSING_MEDIA_GENERATIONS'
                )

            logger.info(f"Processing {len(media_generations)} media generations for video {video.id}")

            # Process in batches for better efficiency
            generated_images = self._process_media_generations_batch(video, media_generations, stage)

            # Update MediaAssets with generated images
            update_result = self._update_media_assets_with_images(video, generated_images)

            # Calculate success metrics
            successful_count = update_result.get('successful_count', 0)
            failed_count = update_result.get('failed_count', 0)
            total_count = len(media_generations)

            logger.info(f"Batch processing completed for video {video.id}: "
                       f"{successful_count}/{total_count} successful, {failed_count} failed")

            # Determine if this should be considered a success or partial failure
            if successful_count == 0:
                return ProviderResponse.failure(
                    error_message=f"All {total_count} image generation jobs failed",
                    error_code='ALL_JOBS_FAILED',
                    metadata={
                        'stage': stage,
                        'provider': 'runpod',
                        'endpoint_id': self.endpoint_id,
                        'total_jobs': total_count,
                        'successful_jobs': successful_count,
                        'failed_jobs': failed_count
                    }
                )
            elif failed_count > 0:
                # Partial success - log warning but return success
                logger.warning(f"Partial success for video {video.id}: "
                              f"{failed_count}/{total_count} jobs failed")

            return ProviderResponse.success(
                data={
                    'generated_images': generated_images,
                    'total_images': total_count,
                    'successful_images': successful_count,
                    'failed_images': failed_count,
                    'media_generations_processed': len(media_generations)
                },
                metadata={
                    'stage': stage,
                    'provider': 'runpod',
                    'endpoint_id': self.endpoint_id,
                    'batch_processing': True,
                    'total_jobs': total_count,
                    'successful_jobs': successful_count,
                    'failed_jobs': failed_count
                }
            )
            
        except ProviderError:
            raise
        except Exception as e:
            logger.error(f"Runpod provider error for stage {stage}: {str(e)}")
            return self.handle_error(e, video, stage)
    
    def prepare_payload(self, video, stage: str) -> Dict[str, Any]:
        """
        Prepare API payload for Runpod
        
        Args:
            video: Video model instance
            stage: Stage name
            
        Returns:
            Dict: Runpod API payload
        """
        # Get image generation parameters
        width, height = self._get_image_dimensions(video.orientation)
        
        # Build Runpod payload using the provided workflow
        payload = {
            "input": {
            "workflow": {
                "6": {
                "inputs": {
                    "text": "",  # Will be filled with the actual prompt
                    "clip": [
                    "30",
                    1
                    ]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {
                    "title": "CLIP Text Encode (Positive Prompt)"
                }
                },
                "8": {
                "inputs": {
                    "samples": [
                    "31",
                    0
                    ],
                    "vae": [
                    "30",
                    2
                    ]
                },
                "class_type": "VAEDecode",
                "_meta": {
                    "title": "VAE Decode"
                }
                },
                "9": {
                "inputs": {
                    "filename_prefix": "ComfyUI",
                    "images": [
                    "8",
                    0
                    ]
                },
                "class_type": "SaveImage",
                "_meta": {
                    "title": "Save Image"
                }
                },
                "27": {
                "inputs": {
                    "width": width,
                    "height": height,
                    "batch_size": 1
                },
                "class_type": "EmptySD3LatentImage",
                "_meta": {
                    "title": "EmptySD3LatentImage"
                }
                },
                "30": {
                "inputs": {
                    "ckpt_name": "flux1-dev-fp8.safetensors"
                },
                "class_type": "CheckpointLoaderSimple",
                "_meta": {
                    "title": "Load Checkpoint"
                }
                },
                "31": {
                "inputs": {
                    "seed": None,  # Use None for random seed, or set as needed
                    "steps": 20,
                    "cfg": 1,
                    "sampler_name": "euler",
                    "scheduler": "simple",
                    "denoise": 1,
                    "model": [
                    "30",
                    0
                    ],
                    "positive": [
                    "35",
                    0
                    ],
                    "negative": [
                    "33",
                    0
                    ],
                    "latent_image": [
                    "27",
                    0
                    ]
                },
                "class_type": "KSampler",
                "_meta": {
                    "title": "KSampler"
                }
                },
                "33": {
                "inputs": {
                    "text": "",
                    "clip": [
                    "30",
                    1
                    ]
                },
                "class_type": "CLIPTextEncode",
                "_meta": {
                    "title": "CLIP Text Encode (Negative Prompt)"
                }
                },
                "35": {
                "inputs": {
                    "guidance": 3.5,
                    "conditioning": [
                    "6",
                    0
                    ]
                },
                "class_type": "FluxGuidance",
                "_meta": {
                    "title": "FluxGuidance"
                }
                }
            },
            "prompt": "",  # Will be filled per image
            },
        }
        
        # Add video style parameters
        if video.video_style:
            payload['input']['style'] = video.video_style
        
        return payload
    
    def handle_response(
        self, 
        response: Any, 
        video, 
        stage: str
    ) -> ProviderResponse:
        """
        Process Runpod API response
        
        Args:
            response: Raw response from Runpod API
            video: Video model instance
            stage: Stage name
            
        Returns:
            ProviderResponse: Standardized response
        """
        try:
            if isinstance(response, dict):
                # Check for successful completion
                if response.get('status') == 'COMPLETED' and 'output' in response:
                    return ProviderResponse.success(
                        data=response,
                        metadata={'stage': stage, 'provider': 'runpod'}
                    )
                elif response.get('status') == 'FAILED':
                    return ProviderResponse.failure(
                        error_message=response.get('error', 'Runpod job failed'),
                        error_code='RUNPOD_JOB_FAILED'
                    )
                elif response.get('status') in ['IN_QUEUE', 'IN_PROGRESS']:
                    return ProviderResponse.pending(
                        execution_id=response.get('id')
                    )
                else:
                    return ProviderResponse.failure(
                        error_message="Unexpected response status from Runpod",
                        error_code='UNEXPECTED_STATUS'
                    )
            else:
                return ProviderResponse.failure(
                    error_message="Invalid response format from Runpod API",
                    error_code='INVALID_RESPONSE_FORMAT'
                )
                
        except Exception as e:
            logger.error(f"Error handling Runpod response for stage {stage}: {str(e)}")
            return self.handle_error(e, video, stage)
    
    def handle_error(
        self, 
        error: Exception, 
        video, 
        stage: str
    ) -> ProviderResponse:
        """
        Handle Runpod provider errors
        
        Args:
            error: Exception that occurred
            video: Video model instance
            stage: Stage name
            
        Returns:
            ProviderResponse: Standardized error response
        """
        error_message = str(error)
        error_code = 'RUNPOD_PROVIDER_ERROR'
        
        # Categorize different types of errors
        if isinstance(error, requests.exceptions.Timeout):
            error_code = 'RUNPOD_TIMEOUT_ERROR'
            error_message = f"Runpod API timeout for stage {stage}"
        elif isinstance(error, requests.exceptions.ConnectionError):
            error_code = 'RUNPOD_CONNECTION_ERROR'
            error_message = f"Failed to connect to Runpod API for stage {stage}"
        elif isinstance(error, requests.exceptions.HTTPError):
            error_code = 'RUNPOD_HTTP_ERROR'
            error_message = f"Runpod API HTTP error for stage {stage}: {error_message}"
        elif isinstance(error, requests.exceptions.RequestException):
            error_code = 'RUNPOD_REQUEST_ERROR'
            error_message = f"Runpod API request failed for stage {stage}: {error_message}"
        elif isinstance(error, ProviderError):
            error_code = error.error_code or 'RUNPOD_PROVIDER_ERROR'
            error_message = error.message
        
        self.log_operation('handle_error', video.id, stage, {
            'error_code': error_code,
            'error_message': error_message
        })
        
        return ProviderResponse.failure(
            error_message=error_message,
            error_code=error_code,
            metadata={
                'provider': self.provider_name,
                'stage': stage,
                'video_id': video.id
            }
        )
    
    def _submit_job(self, payload: Dict[str, Any]) -> dict:
        """
        Submit job to Runpod
        
        Args:
            payload: Job payload
            
        Returns:
            dict: Job submission response
            
        Raises:
            requests.RequestException: If request fails
        """
        try:
            url = f"{self.base_url}/run"
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json',
                'User-Agent': 'AIVIA-VideoAgent/1.0'
            }
            
            logger.info(f"Submitting job to Runpod endpoint")
            
            response = requests.post(
                url,
                json=payload,
                headers=headers,
                timeout=30  # Shorter timeout for job submission
            )
            
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Runpod job submission failed: {str(e)}")
            raise
    
    def _poll_job_completion(self, job_id: str) -> dict:
        """
        Poll job until completion
        
        Args:
            job_id: Job ID to poll
            
        Returns:
            dict: Final job result
            
        Raises:
            ProviderError: If job fails or times out
        """
        try:
            url = f"{self.base_url}/status/{job_id}"
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'User-Agent': 'AIVIA-VideoAgent/1.0'
            }
            
            start_time = time.time()
            
            while time.time() - start_time < self.timeout:
                response = requests.get(url, headers=headers, timeout=10)
                response.raise_for_status()
                
                result = response.json()
                status = result.get('status')
                
                if status == 'COMPLETED':
                    logger.info(f"Runpod job {job_id} completed successfully")
                    return result
                elif status == 'FAILED':
                    error_msg = result.get('error', 'Job failed without error details')
                    raise ProviderError(
                        f"Runpod job failed: {error_msg}",
                        self.provider_name,
                        'image_generation',
                        'RUNPOD_JOB_FAILED'
                    )
                elif status in ['IN_QUEUE', 'IN_PROGRESS']:
                    logger.debug(f"Runpod job {job_id} status: {status}")
                    time.sleep(self.poll_interval)
                else:
                    logger.warning(f"Unknown Runpod job status: {status}")
                    time.sleep(self.poll_interval)
            
            # Timeout reached
            raise ProviderError(
                f"Runpod job {job_id} timed out after {self.timeout} seconds",
                self.provider_name,
                'image_generation',
                'RUNPOD_JOB_TIMEOUT'
            )
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to poll Runpod job {job_id}: {str(e)}")
            raise ProviderError(
                f"Failed to poll job status: {str(e)}",
                self.provider_name,
                'image_generation',
                'RUNPOD_POLL_ERROR'
            )
    
    def _get_media_generations_for_processing(self, video) -> list:
        """
        Get media generations that need image generation

        Args:
            video: Video model instance

        Returns:
            list: List of MediaGeneration objects that need processing
        """
        try:
            # Get image generations that have prompts but no associated media assets with URLs
            from ...models import MediaGeneration, MediaAsset

            media_generations = MediaGeneration.objects.filter(
                video=video,
                media_type='image'
            ).exclude(
                prompt__isnull=True
            ).exclude(
                prompt__exact=''
            ).order_by('prompt_sequence', 'created_at')

            # Filter out generations that already have successful media assets
            generations_needing_processing = []
            for generation in media_generations:
                existing_assets = MediaAsset.objects.filter(
                    generation=generation,
                    type='image'
                ).exclude(
                    source_path__isnull=True
                ).exclude(
                    source_path__exact=''
                )

                if not existing_assets.exists():
                    generations_needing_processing.append(generation)

            return generations_needing_processing

        except Exception as e:
            logger.error(f"Failed to get media generations for video {video.id}: {str(e)}")
            return []

    def _get_image_prompts(self, video) -> list:
        """
        Get image prompts from video's media generations (legacy method)

        Args:
            video: Video model instance

        Returns:
            list: List of image prompts
        """
        try:
            prompts = []

            # Get image prompt generations for this video
            image_generations = video.media_generations.filter(
                media_type='image'
            ).order_by('created_at')

            for generation in image_generations:
                prompts.append(generation.prompt)

            return prompts

        except Exception as e:
            logger.error(f"Failed to get image prompts for video {video.id}: {str(e)}")
            return []
    
    def _get_image_dimensions(self, orientation: str) -> tuple:
        """
        Get image dimensions based on video orientation
        
        Args:
            orientation: Video orientation (landscape, portrait, square)
            
        Returns:
            tuple: (width, height)
        """
        if orientation == 'portrait':
            return (512, 768)  # 2:3 aspect ratio
        elif orientation == 'square':
            return (512, 512)  # 1:1 aspect ratio
        else:  # landscape or default
            return (768, 512)  # 3:2 aspect ratio
    
    def _process_image_response(self, response_data: dict, prompt: str, index: int) -> dict:
        """
        Process individual image generation response
        
        Args:
            response_data: API response data
            prompt: Original prompt
            index: Image index
            
        Returns:
            dict: Processed image data
        """
        try:
            output = response_data.get('output', [])
            if output and len(output) > 0:
                image_url = output[0]  # Get first generated image
                
                return {
                    'url': image_url,
                    'prompt': prompt,
                    'index': index,
                    'metadata': {
                        'provider': 'runpod',
                        'endpoint_id': self.endpoint_id,
                        'job_id': response_data.get('id')
                    }
                }
            else:
                return {
                    'error': 'No image URLs in response',
                    'prompt': prompt,
                    'index': index
                }
                
        except Exception as e:
            logger.error(f"Failed to process image response: {str(e)}")
            return {
                'error': str(e),
                'prompt': prompt,
                'index': index
            }
    
    def _create_media_assets(self, video, generated_images: list):
        """
        Create MediaAsset records for generated images
        
        Args:
            video: Video model instance
            generated_images: List of generated image data
        """
        try:
            from ...models import MediaGeneration, MediaAsset
            
            # Get or create image generation record
            generation, created = MediaGeneration.objects.get_or_create(
                video=video,
                media_type='image',
                media_provider='runpod',
                defaults={
                    'prompt': f"Generated {len(generated_images)} images for video"
                }
            )
            
            # Create MediaAsset for each successful image
            for image_data in generated_images:
                if 'error' not in image_data and image_data.get('url'):
                    MediaAsset.objects.create(
                        generation=generation,
                        source_path=image_data['url'],
                        type='image',
                        metadata={
                            'prompt': image_data.get('prompt', ''),
                            'index': image_data.get('index', 0),
                            'provider': 'runpod',
                            'endpoint_id': self.endpoint_id,
                            'job_id': image_data.get('metadata', {}).get('job_id')
                        }
                    )
            
            logger.info(f"Created MediaAssets for {len([img for img in generated_images if 'error' not in img])} images")
            
        except Exception as e:
            logger.error(f"Failed to create MediaAssets: {str(e)}")

    def _process_media_generations_batch(self, video, media_generations: list, stage: str) -> list:
        """
        Process multiple media generations in batch

        Args:
            video: Video model instance
            media_generations: List of MediaGeneration objects
            stage: Stage name

        Returns:
            list: List of generated image data
        """
        try:
            # Submit all jobs first
            job_submissions = []

            for generation in media_generations:
                try:
                    logger.info(f"Submitting job for generation {generation.id} (sequence {generation.prompt_sequence})")

                    # Prepare API payload
                    api_payload = self.prepare_payload(video, stage)

                    # Set the prompt in the ComfyUI workflow
                    api_payload['input']['workflow']['6']['inputs']['text'] = generation.prompt
                    api_payload['input']['prompt'] = generation.prompt

                    # Submit job to Runpod
                    job_response = self._submit_job(api_payload)
                    job_id = job_response.get('id')

                    if job_id:
                        job_submissions.append({
                            'job_id': job_id,
                            'generation': generation,
                            'prompt': generation.prompt,
                            'sequence': generation.prompt_sequence
                        })
                        logger.info(f"Successfully submitted job {job_id} for generation {generation.id}")
                    else:
                        logger.error(f"Failed to get job ID for generation {generation.id}")
                        job_submissions.append({
                            'error': 'Failed to get job ID',
                            'generation': generation,
                            'prompt': generation.prompt,
                            'sequence': generation.prompt_sequence
                        })

                except Exception as e:
                    logger.error(f"Failed to submit job for generation {generation.id}: {str(e)}")
                    job_submissions.append({
                        'error': str(e),
                        'generation': generation,
                        'prompt': generation.prompt,
                        'sequence': generation.prompt_sequence
                    })

            # Now poll all jobs for completion
            generated_images = self._poll_multiple_jobs(job_submissions)

            return generated_images

        except Exception as e:
            logger.error(f"Critical error in batch processing: {str(e)}")
            # Return error entries for all generations
            error_results = []
            for generation in media_generations:
                error_results.append({
                    'error': f"Batch processing failed: {str(e)}",
                    'prompt': generation.prompt,
                    'sequence': generation.prompt_sequence,
                    'generation_id': generation.id
                })
            return error_results

    def _submit_multiple_jobs(self, video, media_generations: list, stage: str) -> list:
        """
        Submit multiple image generation jobs to RunPod

        Args:
            video: Video model instance
            media_generations: List of MediaGeneration objects
            stage: Stage name

        Returns:
            list: List of job submission results
        """
        job_submissions = []

        for generation in media_generations:
            try:
                logger.info(f"Submitting job for generation {generation.id}")

                # Prepare API payload
                api_payload = self.prepare_payload(video, stage)

                # Set the prompt in the ComfyUI workflow
                api_payload['input']['workflow']['6']['inputs']['text'] = generation.prompt
                api_payload['input']['prompt'] = generation.prompt

                # Submit job to Runpod
                job_response = self._submit_job(api_payload)
                job_id = job_response.get('id')

                if job_id:
                    job_submissions.append({
                        'job_id': job_id,
                        'generation': generation,
                        'status': 'submitted',
                        'prompt': generation.prompt
                    })
                else:
                    job_submissions.append({
                        'generation': generation,
                        'status': 'failed',
                        'error': 'Failed to get job ID',
                        'prompt': generation.prompt
                    })

            except Exception as e:
                error_msg = f"Failed to submit job for generation {generation.id}: {str(e)}"
                logger.error(error_msg)
                job_submissions.append({
                    'generation': generation,
                    'status': 'failed',
                    'error': error_msg,
                    'prompt': generation.prompt,
                    'retry_count': 0,
                    'max_retries': self.max_retries
                })

        return job_submissions

    def _poll_multiple_jobs(self, job_submissions: list) -> list:
        """
        Poll multiple jobs concurrently for completion

        Args:
            job_submissions: List of job submission results

        Returns:
            list: List of generated image data
        """
        generated_images = []

        # Filter out failed submissions
        valid_jobs = [job for job in job_submissions if 'job_id' in job and 'error' not in job]
        failed_jobs = [job for job in job_submissions if 'error' in job]

        # Add failed jobs to results
        for failed_job in failed_jobs:
            generated_images.append({
                'error': failed_job.get('error', 'Unknown error'),
                'prompt': failed_job.get('prompt', ''),
                'sequence': failed_job.get('sequence', 0),
                'generation_id': failed_job.get('generation', {}).id if failed_job.get('generation') else None
            })

        if not valid_jobs:
            logger.warning("No valid jobs to poll")
            return generated_images

        # Use ThreadPoolExecutor for concurrent polling
        with ThreadPoolExecutor(max_workers=min(self.max_concurrent_polls, len(valid_jobs))) as executor:
            # Submit polling tasks
            future_to_job = {
                executor.submit(self._poll_single_job_with_retry, job): job
                for job in valid_jobs
            }

            # Collect results as they complete
            for future in as_completed(future_to_job):
                job = future_to_job[future]
                try:
                    result = future.result()
                    if result:
                        generated_images.append(result)
                    else:
                        # Handle case where polling returned None
                        generated_images.append({
                            'error': 'Polling returned no result',
                            'prompt': job.get('prompt', ''),
                            'sequence': job.get('sequence', 0),
                            'generation_id': job.get('generation', {}).id if job.get('generation') else None
                        })
                except Exception as e:
                    logger.error(f"Error polling job {job.get('job_id')}: {str(e)}")
                    generated_images.append({
                        'error': str(e),
                        'prompt': job.get('prompt', ''),
                        'sequence': job.get('sequence', 0),
                        'generation_id': job.get('generation', {}).id if job.get('generation') else None
                    })

        return generated_images

    def _poll_single_job_with_retry(self, job: dict) -> dict:
        """
        Poll a single job with retry logic

        Args:
            job: Job submission data

        Returns:
            dict: Generated image data or None if failed
        """
        job_id = job.get('job_id')
        generation = job.get('generation')
        prompt = job.get('prompt', '')
        sequence = job.get('sequence', 0)

        try:
            # Poll for completion with retries
            for attempt in range(self.max_retries):
                try:
                    result_data = self._poll_job_completion(job_id)

                    # Process successful result
                    image_data = self._process_image_response(result_data, prompt, sequence)
                    image_data['generation_id'] = generation.id if generation else None
                    image_data['sequence'] = sequence

                    return image_data

                except Exception as e:
                    logger.warning(f"Attempt {attempt + 1} failed for job {job_id}: {str(e)}")
                    if attempt < self.max_retries - 1:
                        time.sleep(2 ** attempt)  # Exponential backoff
                    else:
                        raise e

        except Exception as e:
            logger.error(f"Failed to poll job {job_id} after {self.max_retries} attempts: {str(e)}")
            return {
                'error': str(e),
                'prompt': prompt,
                'sequence': sequence,
                'generation_id': generation.id if generation else None
            }

        return None

    def _update_media_assets_with_images(self, video, generated_images: list):
        """
        Update MediaAsset records with generated image URLs

        Args:
            video: Video model instance
            generated_images: List of generated image data
        """
        try:
            from ...models import MediaAsset

            successful_images = [img for img in generated_images if 'error' not in img and img.get('url')]
            failed_images = [img for img in generated_images if 'error' in img]

            logger.info(f"Updating MediaAssets: {len(successful_images)} successful, {len(failed_images)} failed")

            # Update successful images
            for image_data in successful_images:
                generation_id = image_data.get('generation_id')
                sequence = image_data.get('sequence', 0)

                if generation_id:
                    try:
                        # Find or create MediaAsset for this generation
                        media_asset, created = MediaAsset.objects.get_or_create(
                            generation_id=generation_id,
                            video=video,
                            type='image',
                            defaults={
                                'source_path': image_data['url'],
                                'sequence_order': sequence,
                                'metadata': {
                                    'prompt': image_data.get('prompt', ''),
                                    'provider': 'runpod',
                                    'endpoint_id': self.endpoint_id,
                                    'job_id': image_data.get('metadata', {}).get('job_id'),
                                    'sequence': sequence
                                }
                            }
                        )

                        if not created and not media_asset.source_path:
                            # Update existing asset that doesn't have a source path
                            media_asset.source_path = image_data['url']
                            media_asset.metadata.update({
                                'prompt': image_data.get('prompt', ''),
                                'provider': 'runpod',
                                'endpoint_id': self.endpoint_id,
                                'job_id': image_data.get('metadata', {}).get('job_id'),
                                'sequence': sequence
                            })
                            media_asset.save()

                        logger.info(f"Updated MediaAsset {media_asset.id} for generation {generation_id}")

                    except Exception as e:
                        logger.error(f"Failed to update MediaAsset for generation {generation_id}: {str(e)}")
                else:
                    logger.warning(f"No generation_id found for image data: {image_data}")

            # Log failed images
            for failed_image in failed_images:
                generation_id = failed_image.get('generation_id')
                error = failed_image.get('error', 'Unknown error')
                logger.error(f"Failed to generate image for generation {generation_id}: {error}")

            return {
                'successful_count': len(successful_images),
                'failed_count': len(failed_images),
                'total_count': len(generated_images)
            }

        except Exception as e:
            logger.error(f"Failed to update MediaAssets: {str(e)}")
            return {
                'successful_count': 0,
                'failed_count': len(generated_images),
                'total_count': len(generated_images),
                'error': str(e)
            }